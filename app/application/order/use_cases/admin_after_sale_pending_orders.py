from app.domain.order.repositories import OrderRepository
from app.domain.order.value_objects import SubOrderStatus
from app.domain.product.services import ProductService
from app.extensions import db
from app.infrastructure.order.models import OrderModel


class AdminAfterSalePendingOrdersUseCase:
    """管理员查询待售后订单用例"""

    def __init__(
            self,
            order_repo: OrderRepository,
            product_service: ProductService
    ):
        self.order_repo = order_repo
        self.product_service = product_service

    def execute(self, page: int, per_page: int) -> dict:
        """执行获取所有待售后订单

        获取所有状态为售后待处理的子订单，带分页，返回子订单号、状态、商品信息等

        Args:
            page: 页码
            per_page: 每页数量

        Returns:
            dict: 包含子订单列表和分页信息的字典
        """
        # 获取待售后处理子订单分页数据
        sub_orders, total = self.order_repo.find_paginated_sub_orders_by_status(
            status=SubOrderStatus.AFTER_SALE_PENDING,
            page=page,
            per_page=per_page
        )

        # 收集所有子订单的父订单ID
        parent_order_ids = [so.order_id for so in sub_orders]

        # 批量查询父订单信息，避免N+1问题
        parent_orders = {}
        if parent_order_ids:
            parent_stmt = db.select(OrderModel).where(
                OrderModel.id.in_(parent_order_ids),
                OrderModel.deleted_at.is_(None)
            )
            parent_result = db.session.execute(parent_stmt)
            parent_order_models = parent_result.scalars().all()
            parent_orders = {order.id: order for order in parent_order_models}

        items = []
        for so in sub_orders:
            # 获取SKU和商品信息
            sku_info = self.product_service.get_product_sku_info(so.sku_id)

            # 获取父订单信息
            parent_order = parent_orders.get(so.order_id)

            # 准备地址和联系人信息
            address_full = ""
            contact_info = ""

            if parent_order and parent_order.shipping_address:
                # 解析父订单的收货地址信息
                shipping_address = parent_order.shipping_address

                # 构建完整地址
                if isinstance(shipping_address, dict):
                    province = shipping_address.get('province', '')
                    city = shipping_address.get('city', '')
                    district = shipping_address.get('district', '')
                    detail = shipping_address.get('detail', '')
                    address_full = f"{province}{city}{district}{detail}"

                    # 构建联系人信息
                    contact_name = shipping_address.get('contact_name', '')
                    contact_phone = shipping_address.get('contact_phone', '')
                    contact_info = f"{contact_name} {contact_phone}"

            # 构建子订单数据
            items.append({
                "order_no": so.order_no,
                "status": so.status.value,
                "product_title": sku_info["title"],
                "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
                "sku_display_title": sku_info["sku"]["display_title"],
                "quantity": so.quantity,
                "address_full": address_full,  # 添加完整地址
                "contact_info": contact_info  # 添加联系人信息
            })

        return {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
